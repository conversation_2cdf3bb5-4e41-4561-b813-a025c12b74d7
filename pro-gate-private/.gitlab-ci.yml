include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# PRO-GATE-PRIVATE: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
pro_gate_private_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "pro-gate-private"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate-private/**


pro_gate_private_helm_kubeval_testing_develop:
  stage: test
  needs:
    - job: pro_gate_private_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "pro-gate-private"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate-private/**
        - charts/pro-gate-private/**


pro_gate_private_deploy_chart_develop:
  stage: deploy
  needs:
    - pro_gate_private_helm_kubeval_testing_develop
    - job: pro_gate_private_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "pro-gate-private"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - pro-gate-private/**
        - charts/pro-gate-private/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - pro-gate-private/**

pro_gate_private_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-gate-private"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

pro_gate_private_helm_kubeval_testing_tag:
  stage: test
  needs:
    - pro_gate_private_build_tag
  variables:
    SERVICE_NAME: "pro-gate-private"
  extends:
    - .validate_helm_template
  <<: *tag_rules


pro_gate_private_deploy_chart_tag:
  stage: deploy
  needs:
    - pro_gate_private_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-gate-private"
  extends:
    - .deploy_helm_template
  <<: *tag_rules

pro_gate_private_deploy_prod:
  stage: deploy
  needs:
    - pro_gate_private_deploy_chart_tag
  variables:
    STAGE: "prod"
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "pro-gate-private"
    NAMESPACE: "$KUBE_NAMESPACE-prod"
  extends:
    - .deploy_helm_template
  when: manual
  <<: *tag_rules