include:
  - project: 'tkp3/infra'
    file: '/gitlab_templates/java-autoload.templates.yaml'
    ref: OPDVST-2352-security-updates

# ========================
# MANIFEST-PROCESSING: build + deploy (docker + helm)
# ========================

# --- DEVELOP ветка ---
manifest-processing_build_develop:
  stage: build
  needs: [develop_build_test_publish]
  variables:
    SERVICE_NAME: "manifest-processing"
    TAG: "$CI_COMMIT_SHORT_SHA"
  extends:
    - .docker_gradle_build_and_push
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - manifest-processing/**
        - ./**


manifest-processing_helm_kubeval_testing_develop:
  stage: test
  needs:
    - job: manifest-processing_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    SERVICE_NAME: "manifest-processing"
  extends:
    - .validate_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - manifest-processing/**
        - charts/manifest-processing/**


manifest-processing_deploy_chart_develop:
  stage: deploy
  needs:
    - manifest-processing_helm_kubeval_testing_develop
    - job: manifest-processing_build_develop
      optional: true # need to run re-deploy on chart change without rebuilding
  variables:
    STAGE: "dev"
    SERVICE_NAME: "manifest-processing"
  extends:
    - .deploy_helm_template
  rules:
    - if: $CI_COMMIT_REF_NAME == "develop" && $CI_COMMIT_TAG == null
      changes:
        - manifest-processing/**
        - charts/manifest-processing/**

# --- TAG ---
.tag_rules: &tag_rules
  rules:
    - if: $CI_COMMIT_TAG
      changes:
        - manifest-processing/**

manifest-processing_build_tag:
  stage: build
  variables:
    TAG: "$CI_COMMIT_TAG"
    SERVICE_NAME: "manifest-processing"
  extends:
    - .docker_gradle_build_and_push
  <<: *tag_rules

manifest-processing_helm_kubeval_testing_tag:
  stage: test
  needs:
    - manifest-processing_build_tag
  extends:
    - .validate_helm_template
  <<: *tag_rules

manifest-processing_deploy_chart_tag:
  stage: deploy
  needs:
    - manifest-processing_helm_kubeval_testing_tag
  variables:
    STAGE: "dev"
    TAG: "$CI_COMMIT_TAG"
  extends:
    - .deploy_helm_template
  <<: *tag_rules 