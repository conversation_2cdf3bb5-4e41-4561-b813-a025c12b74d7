# Default values for pro-manifest-processing.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: pro-manifest-processing

replicaCount: 1

image:
  repository: pro-manifest-processing
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

service:
  http:
    type: ClusterIP
    port: 8080
    targetPort: 8080
  grpc:
    type: ClusterIP
    port: 5000
    targetPort: 5000

resources:
  limits:
    cpu: 1000m
    memory: 512Mi
  requests:
    cpu: 100m
    memory: 128Mi

env:
  system_user_id: ""
  client:
    logging:
      enable: true
  kafka:
    servers: ""
  zookeeper:
    nodes: ""
  service:
    abt_gate: ""
