{{- if .Values.gateway.enabled -}}
kind: Gateway
apiVersion: networking.istio.io/v1beta1
metadata:
  name: pro-ui-gateway
  namespace: istio-ingressgateway
spec:
  servers:
    - port:
        number: 443
        protocol: HTTPS
        name: https
      hosts:
        - crm-pro-dev.sbertroika.tech
      tls:
        mode: SIMPLE
        credentialName: wildcard-sbertroika-tech
  selector:
    istio: ingressgateway
status: {}
{{- end }}