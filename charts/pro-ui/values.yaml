# Default values for pro-ui.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

application: pro-ui

replicaCount: 1

image:
  repository: pro-ui
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "dev"

imagePullSecrets:
  - name: "default-secret"
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: { }

podSecurityContext: { }

securityContext: { }

service:
  http:
    type: ClusterIP
    port: 80
    targetPort: 80

ingress:
  enabled: false
  className: ""
  annotations: {}
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: Prefix
  tls: []

resources:
  limits:
    cpu: 500m
    memory: 256Mi
  requests:
    cpu: 250m
    memory: 128Mi

nodeSelector: { }

tolerations: [ ]

affinity: { }

# disable right now due not Java app
livenessProbe:
  enabled: false
  path: /actuator/health/liveness
  port: 8080
  initialDelaySeconds: 10
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1

readinessProbe:
  enabled: false
  path: /actuator/health/readiness
  port: 8080
  initialDelaySeconds: 60
  periodSeconds: 30
  failureThreshold: 3
  successThreshold: 1
  timeoutSeconds: 1

gateway:
  enabled: true