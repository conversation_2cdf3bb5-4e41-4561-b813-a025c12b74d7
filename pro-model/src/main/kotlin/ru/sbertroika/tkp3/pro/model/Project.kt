package ru.sbertroika.tkp3.pro.model

import org.springframework.data.annotation.Id
import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDate
import java.util.*

/**
 * Составной первичный ключ для таблицы проектов
 * @param pId Уникальный идентификатор проекта
 * @param pVersion Версия записи проекта
 */
data class ProjectPK(
    val pId: UUID? = null,
    val pVersion: Int? = null
)

@Table("project")
data class Project(

    /** Уникальный идентификатор проекта */
    @Id
    @Column("pr_id")
    var id: UUID? = null,

    /** Версия записи проекта для поддержки версионирования */
    @Column("pr_version")
    var version: Int? = null,

    /** Дата и время создания версии */
    @Column("pr_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    /** Идентификатор пользователя, создавшего версию */
    @Column("pr_version_created_by")
    var versionCreatedBy: UUID? = null,

    /** Дата начала проекта */
    @Column("pr_start_date")
    var startDate: LocalDate? = null,

    /** Дата окончания проекта */
    @Column("pr_end_date")
    var endDate: LocalDate? = null,

    /** Наименование проекта */
    @Column("pr_name")
    var name: String? = null,

    /** Код проекта (например, ST-PRO-MSK-001) */
    @Column("pr_code")
    var code: String? = null,

    /** Тип проекта (transport_system, metro_system, bus_system и т.д.) */
    @Column("pr_project_type")
    var projectType: String? = null,

    /** Статус проекта (ACTIVE, DRAFT, COMPLETED и т.д.) */
    @Column("pr_status")
    var status: ProjectStatus? = null,

    /** Идентификатор связанного договора (UUID) */
    @Column("pr_contract_id")
    var contractId: UUID? = null,

    /** Номер связанного договора */
    @Column("pr_contract_number")
    var contractNumber: String? = null,

    /** Наименование договора */
    @Column("pr_contract_name")
    var contractName: String? = null,

    /** Описание проекта */
    @Column("pr_description")
    var description: String? = null,

    /** Регион проекта */
    @Column("pr_region")
    var region: String? = null,

    /** Общий бюджет проекта в копейках */
    @Column("pr_total_budget")
    var totalBudget: Long? = null,

    /** Потраченный бюджет проекта в копейках */
    @Column("pr_spent_budget")
    var spentBudget: Long? = null,

    /** Прогресс выполнения проекта (0-100) */
    @Column("pr_progress")
    var progress: Int? = null,

    /** Менеджер проекта */
    @Column("pr_manager")
    var manager: String? = null,

    /** Дата последней синхронизации с внешними системами */
    @Column("pr_last_sync_date")
    var lastSyncDate: Timestamp? = null,

    /** Статус синхронизации (pending, synced, error) */
    @Column("pr_sync_status")
    var syncStatus: String? = null,

    /** Порядковый номер проекта в системе СТ */
    @Column("p_index")
    var index: Int? = null,

    /** Теги проекта в формате JSON */
    @Column("tags")
    var tags: String? = null,

    /** Операторская организация (заполняется из связанной таблицы project_operator) */
    var operatorOrganization: OperatorOrganization? = null,

    /** Активные терминалы (вычисляемое поле) */
    var activeTerminals: Int = 0,

    /** Общее количество терминалов (вычисляемое поле) */
    var terminals: Int = 0,

    /** Количество транспортных средств (вычисляемое поле) */
    var vehicles: Int = 0,

    /** Маршруты (вычисляемое поле) */
    var routes: List<String> = emptyList(),

    /** Количество транзакций в месяц (вычисляемое поле) */
    var monthlyTransactions: Int = 0,

    /** Доход в месяц в копейках (вычисляемое поле) */
    var monthlyRevenue: Long = 0
)

/**
 * Операторская организация
 */
data class OperatorOrganization(
    val id: UUID? = null,
    val name: String? = null,
    val role: String? = null
)

/**
 * Статусы проекта СберТройка ПРО
 * 
 * Определяет возможные состояния проекта в системе.
 * 
 * @property TEST Тестовый проект
 * @property DEMO Демонстрационный проект
 * @property ACTIVE Активный проект
 * @property ARCHIVE Архивный проект
 * @property NSI_CONFLICT Проект с конфликтом в нормативно-справочной информации
 * @property DISABLED Отключенный проект
 * @property BLOCKED Заблокированный проект
 * @property IS_DELETED Удаленный проект (логическое удаление)
 * @property DRAFT Черновик проекта
 * @property EXPIRING Истекающий проект
 * @property COMPLETED Завершенный проект
 */
enum class ProjectStatus {
    TEST, DEMO, ACTIVE, ARCHIVE, NSI_CONFLICT, DISABLED, BLOCKED, IS_DELETED, DRAFT, EXPIRING, COMPLETED
}