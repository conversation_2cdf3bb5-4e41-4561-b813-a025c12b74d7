import { getAuthToken } from '@/utils/auth-token';

// Глобальная переменная для отслеживания обновления токена
let isRefreshingToken = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
    failedQueue.forEach(prom => {
        if (error) {
            prom.reject(error);
        } else {
            prom.resolve(token);
        }
    });
    
    failedQueue = [];
};

/**
 * Базовый HTTP клиент для работы с REST API
 */
class ApiClient {
    constructor(baseURL = 'http://localhost:8083') {
        this.baseURL = baseURL;
    }

    /**
     * Выполнить HTTP запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} options - Опции запроса
     * @returns {Promise<Object>} Ответ от сервера
     */
    async request(endpoint, options = {}) {
        const url = `${this.baseURL}${endpoint}`;
        
        // Получаем JWT токен
        const token = getAuthToken();
        
        const defaultOptions = {
            headers: {
                'Content-Type': 'application/json',
                ...(token && { 'Authorization': `Bearer ${token}` }),
                ...options.headers
            }
        };

        const config = {
            ...defaultOptions,
            ...options
        };

        try {
            const response = await fetch(url, config);
            
            // Если получили 401 Unauthorized, возможно токен истек
            if (response.status === 401) {
                console.warn('Received 401 Unauthorized, token might be expired');
                
                // Пытаемся обновить токен
                if (!isRefreshingToken) {
                    isRefreshingToken = true;
                    
                    try {
                        // Динамический импорт auth store для обновления токена
                        const { useAuthStore } = await import('@/stores/auth');
                        const authStore = useAuthStore();
                        
                        const refreshed = await authStore.updateToken();
                        if (refreshed) {
                            // Повторяем запрос с новым токеном
                            const newToken = getAuthToken();
                            const newConfig = {
                                ...config,
                                headers: {
                                    ...config.headers,
                                    'Authorization': `Bearer ${newToken}`
                                }
                            };
                            
                            const newResponse = await fetch(url, newConfig);
                            if (newResponse.ok) {
                                const data = await newResponse.json();
                                processQueue(null, newToken);
                                return data;
                            }
                        }
                    } catch (refreshError) {
                        console.error('Failed to refresh token:', refreshError);
                        processQueue(refreshError, null);
                        
                        // Если не удалось обновить токен, перенаправляем на логин
                        const { useAuthStore } = await import('@/stores/auth');
                        const authStore = useAuthStore();
                        await authStore.login();
                        throw new Error('Authentication required');
                    } finally {
                        isRefreshingToken = false;
                    }
                } else {
                    // Если уже обновляем токен, добавляем запрос в очередь
                    return new Promise((resolve, reject) => {
                        failedQueue.push({ resolve, reject });
                    });
                }
            }
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            return data;
        } catch (error) {
            console.error('API request failed:', error);
            throw error;
        }
    }

    /**
     * GET запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} params - Параметры запроса
     * @returns {Promise<Object>} Ответ от сервера
     */
    async get(endpoint, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const url = queryString ? `${endpoint}?${queryString}` : endpoint;
        
        return this.request(url, {
            method: 'GET'
        });
    }

    /**
     * POST запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} data - Данные для отправки
     * @returns {Promise<Object>} Ответ от сервера
     */
    async post(endpoint, data) {
        return this.request(endpoint, {
            method: 'POST',
            body: JSON.stringify(data)
        });
    }

    /**
     * PUT запрос
     * @param {string} endpoint - Конечная точка API
     * @param {Object} data - Данные для отправки
     * @returns {Promise<Object>} Ответ от сервера
     */
    async put(endpoint, data) {
        return this.request(endpoint, {
            method: 'PUT',
            body: JSON.stringify(data)
        });
    }

    /**
     * DELETE запрос
     * @param {string} endpoint - Конечная точка API
     * @returns {Promise<Object>} Ответ от сервера
     */
    async delete(endpoint) {
        return this.request(endpoint, {
            method: 'DELETE'
        });
    }
}

// Создаем экземпляр клиента для abt-sbol-controller (билетное меню)
export const sbolApiClient = new ApiClient('http://localhost:8083');

// Создаем экземпляр клиента для abt-controller (абонементы, кошельки, шаблоны)
export const abtApiClient = new ApiClient('http://localhost:8086');

// Создаем экземпляр клиента для pro-controller (проекты)
export const proApiClient = new ApiClient('http://localhost:8085');

// Создаем экземпляр клиента для tms-controller (терминалы)
export const tmsApiClient = new ApiClient('http://localhost:8088'); 