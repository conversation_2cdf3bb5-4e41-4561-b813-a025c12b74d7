<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted } from 'vue';
import { FilterMatchMode } from '@primevue/core/api';
import { VehicleService } from '@/service/VehicleService';

const route = useRoute();
const router = useRouter();
const projectCode = route.params.code;

const vehicles = ref([]);
const loading = ref(true);
const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    registrationNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    model: { value: null, matchMode: FilterMatchMode.CONTAINS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS }
});

const statuses = ref([
    { label: 'Активное', value: 'active' },
    { label: 'На обслуживании', value: 'maintenance' },
    { label: 'Неактивное', value: 'inactive' }
]);

const fuelTypes = ref([
    { label: 'Дизель', value: 'diesel' },
    { label: 'Бензин', value: 'gasoline' },
    { label: 'Газ', value: 'gas' },
    { label: 'Электричество', value: 'electric' }
]);

const initFilters = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        registrationNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        model: { value: null, matchMode: FilterMatchMode.CONTAINS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS }
    };
};

onMounted(() => {
    loadVehicles();
});

const loadVehicles = async () => {
    try {
        loading.value = true;
        const data = await VehicleService.getVehiclesByProject(projectCode);
        vehicles.value = data;
    } catch (error) {
        console.error('Ошибка загрузки транспортных средств:', error);
        vehicles.value = [];
    } finally {
        loading.value = false;
    }
};

const clearFilter = () => {
    initFilters();
};

const refreshVehicles = () => {
    loadVehicles();
};

const createVehicle = () => {
    router.push(`/pro/${projectCode}/nsi/vehicle/create`);
};

const editVehicle = (vehicle) => {
    router.push(`/pro/${projectCode}/nsi/vehicle/${vehicle.id}/edit`);
};

const deleteVehicle = async (vehicle) => {
    if (confirm(`Вы уверены, что хотите удалить транспортное средство "${vehicle.registrationNumber}"?`)) {
        try {
            await VehicleService.deleteVehicle(vehicle.id);
            console.log('Транспортное средство удалено:', vehicle.id);
            await loadVehicles();
        } catch (error) {
            console.error('Ошибка удаления транспортного средства:', error);
        }
    }
};

const importFromFile = () => {
    console.log('Загрузить транспортные средства из файла для проекта:', projectCode);
};

const exportData = () => {
    console.log('Экспорт транспортных средств проекта:', projectCode);
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'active': return 'success';
        case 'maintenance': return 'warning';
        case 'inactive': return 'secondary';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'active': return 'Активное';
        case 'maintenance': return 'На обслуживании';
        case 'inactive': return 'Неактивное';
        default: return 'Неизвестно';
    }
};

const getFuelTypeLabel = (fuelType) => {
    switch (fuelType) {
        case 'diesel': return 'Дизель';
        case 'gasoline': return 'Бензин';
        case 'gas': return 'Газ';
        case 'electric': return 'Электричество';
        default: return 'Неизвестно';
    }
};

const getFuelTypeIcon = (fuelType) => {
    switch (fuelType) {
        case 'diesel': return 'pi pi-circle-fill';
        case 'gasoline': return 'pi pi-circle-fill';
        case 'gas': return 'pi pi-circle-fill';
        case 'electric': return 'pi pi-bolt';
        default: return 'pi pi-circle';
    }
};

const getFuelTypeColor = (fuelType) => {
    switch (fuelType) {
        case 'diesel': return '#8b5a2b';
        case 'gasoline': return '#dc2626';
        case 'gas': return '#2563eb';
        case 'electric': return '#16a34a';
        default: return '#6b7280';
    }
};
</script>

<template>
    <div class="vehicle-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">Транспортные средства</h2>
            <div class="flex gap-2">
                <Button 
                    label="Загрузить из файла" 
                    icon="pi pi-upload" 
                    outlined 
                    @click="importFromFile"
                />
                <Button 
                    label="Экспорт" 
                    icon="pi pi-download" 
                    outlined 
                    @click="exportData"
                />
            </div>
        </div>
        
        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <Button 
                    label="Создать" 
                    icon="pi pi-plus" 
                    @click="createVehicle"
                />
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                    <IconField>
                        <InputIcon>
                            <i class="pi pi-search" />
                        </InputIcon>
                        <InputText 
                            v-model="filters.global.value" 
                            placeholder="Поиск по всем полям" 
                        />
                    </IconField>
                </div>
            </div>
            
            <DataTable
                :value="vehicles"
                :paginator="true"
                :rows="10"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['registrationNumber', 'model', 'manufacturer', 'organizationName']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-info-circle text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Транспортные средства не найдены</p>
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="registrationNumber" header="Гос. номер" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по номеру" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-semibold font-mono">{{ data.registrationNumber }}</span>
                    </template>
                </Column>
                
                <Column field="model" header="Модель" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по модели" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.model }}</div>
                            <small class="text-color-secondary">{{ data.manufacturer }} ({{ data.year }})</small>
                        </div>
                    </template>
                </Column>
                
                <Column header="Тип топлива" style="min-width: 130px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i 
                                :class="getFuelTypeIcon(data.fuelType)"
                                :style="{ color: getFuelTypeColor(data.fuelType) }"
                                class="mr-2"
                            ></i>
                            <span>{{ getFuelTypeLabel(data.fuelType) }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column field="capacity" header="Вместимость" :sortable="true" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-users mr-2 text-color-secondary"></i>
                            <span>{{ data.capacity }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по организации" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true" style="min-width: 140px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="statuses" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите статус" 
                            class="p-column-filter" 
                            showClear 
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 120px">
                    <template #body="{ data }">
                        <div class="flex gap-2">
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editVehicle(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteVehicle(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.vehicle-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
