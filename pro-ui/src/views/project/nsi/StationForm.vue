<script setup>
import { useRoute, useRouter } from 'vue-router';
import { ref, onMounted, computed } from 'vue';
import { StationService } from '@/service/StationService';

const route = useRoute();
const router = useRouter();

const projectCode = route.params.code;
const stationId = route.params.stationId;
const isEdit = computed(() => !!stationId);

const loading = ref(false);
const saving = ref(false);

// Форма данных
const form = ref({
    name: '',
    latinName: '',
    city: '',
    district: '',
    region: '',
    country: 'Россия',
    hasCoordinates: false,
    coordinates: ''
});

// Валидация
const errors = ref({});

onMounted(() => {
    if (isEdit.value) {
        loadStation();
    }
});

const loadStation = async () => {
    try {
        loading.value = true;
        const station = await StationService.getStationById(stationId);
        if (station) {
            form.value = { ...station };
        }
    } catch (error) {
        console.error('Ошибка загрузки остановки:', error);
        // Показать уведомление об ошибке
    } finally {
        loading.value = false;
    }
};

const validateForm = () => {
    errors.value = {};
    
    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование обязательно для заполнения';
    }
    
    if (!form.value.latinName.trim()) {
        errors.value.latinName = 'Латинское наименование обязательно для заполнения';
    }
    
    if (!form.value.city.trim()) {
        errors.value.city = 'Город обязателен для заполнения';
    }
    
    if (!form.value.region.trim()) {
        errors.value.region = 'Регион обязателен для заполнения';
    }
    
    if (form.value.hasCoordinates && !form.value.coordinates.trim()) {
        errors.value.coordinates = 'Координаты обязательны при установленном признаке';
    }
    
    return Object.keys(errors.value).length === 0;
};

const saveStation = async () => {
    if (!validateForm()) {
        return;
    }
    
    try {
        saving.value = true;
        
        if (isEdit.value) {
            await StationService.updateStation(stationId, form.value);
            console.log('Остановка обновлена:', form.value);
        } else {
            await StationService.createStation(projectCode, form.value);
            console.log('Остановка создана:', form.value);
        }
        
        // Возвращаемся к списку остановок
        router.push(`/pro/${projectCode}/nsi/station`);
        
    } catch (error) {
        console.error('Ошибка сохранения остановки:', error);
        // Показать уведомление об ошибке
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push(`/pro/${projectCode}/nsi/station`);
};

const onCoordinatesToggle = () => {
    if (!form.value.hasCoordinates) {
        form.value.coordinates = '';
        delete errors.value.coordinates;
    }
};
</script>

<template>
    <div class="station-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h2 class="text-xl font-semibold m-0">
                {{ isEdit ? 'Редактирование остановки' : 'Создание остановки' }}
            </h2>
            <Button 
                label="Назад к списку" 
                icon="pi pi-arrow-left" 
                outlined 
                @click="cancel"
            />
        </div>
        
        <div class="card" v-if="!loading">
            <form @submit.prevent="saveStation">
                <div class="grid">
                    <!-- Основная информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование *</label>
                            <InputText 
                                id="name"
                                v-model="form.name" 
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="Введите наименование остановки"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="latinName" class="font-medium">Латинское наименование *</label>
                            <InputText 
                                id="latinName"
                                v-model="form.latinName" 
                                :class="{ 'p-invalid': errors.latinName }"
                                placeholder="Введите латинское наименование"
                                class="w-full"
                            />
                            <small v-if="errors.latinName" class="p-error">{{ errors.latinName }}</small>
                        </div>
                    </div>
                    
                    <!-- Географическая информация -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Географическая информация</h3>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="city" class="font-medium">Город *</label>
                            <InputText 
                                id="city"
                                v-model="form.city" 
                                :class="{ 'p-invalid': errors.city }"
                                placeholder="Введите город"
                                class="w-full"
                            />
                            <small v-if="errors.city" class="p-error">{{ errors.city }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="district" class="font-medium">Район</label>
                            <InputText 
                                id="district"
                                v-model="form.district" 
                                placeholder="Введите район"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="region" class="font-medium">Регион *</label>
                            <InputText 
                                id="region"
                                v-model="form.region" 
                                :class="{ 'p-invalid': errors.region }"
                                placeholder="Введите регион"
                                class="w-full"
                            />
                            <small v-if="errors.region" class="p-error">{{ errors.region }}</small>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="country" class="font-medium">Страна</label>
                            <InputText 
                                id="country"
                                v-model="form.country" 
                                placeholder="Введите страну"
                                class="w-full"
                            />
                        </div>
                    </div>
                    
                    <!-- Координаты -->
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3 mt-4">Координаты</h3>
                    </div>
                    
                    <div class="col-12">
                        <div class="field">
                            <div class="flex align-items-center">
                                <Checkbox 
                                    id="hasCoordinates" 
                                    v-model="form.hasCoordinates" 
                                    :binary="true"
                                    @change="onCoordinatesToggle"
                                />
                                <label for="hasCoordinates" class="ml-2 font-medium">
                                    Есть координаты
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-12 md:col-6" v-if="form.hasCoordinates">
                        <div class="field">
                            <label for="coordinates" class="font-medium">Координаты *</label>
                            <InputText 
                                id="coordinates"
                                v-model="form.coordinates" 
                                :class="{ 'p-invalid': errors.coordinates }"
                                placeholder="Например: 55.7558, 37.6176"
                                class="w-full"
                            />
                            <small v-if="errors.coordinates" class="p-error">{{ errors.coordinates }}</small>
                            <small class="text-color-secondary">Формат: широта, долгота</small>
                        </div>
                    </div>
                </div>
                
                <!-- Кнопки действий -->
                <div class="flex justify-content-end gap-2 mt-4">
                    <Button 
                        label="Отмена" 
                        icon="pi pi-times" 
                        outlined 
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button 
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать'" 
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>
        
        <!-- Загрузка -->
        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных остановки...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.station-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
