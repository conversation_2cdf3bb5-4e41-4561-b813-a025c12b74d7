<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { FilterMatchMode } from '@primevue/core/api';
import { TerminalService } from '@/service/TerminalService';

const router = useRouter();

const terminals = ref([]);
const loading = ref(true);

const filters = ref({
    global: { value: null, matchMode: FilterMatchMode.CONTAINS },
    serialNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
    location: { value: null, matchMode: FilterMatchMode.CONTAINS },
    status: { value: null, matchMode: FilterMatchMode.EQUALS },
    organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
});

const statuses = ref([
    { label: 'Онлайн', value: 'online' },
    { label: 'Офлайн', value: 'offline' },
    { label: 'Обслуживание', value: 'maintenance' },
    { label: 'Предупреждение', value: 'warning' }
]);

onMounted(() => {
    loadTerminals();
});

const loadTerminals = async () => {
    try {
        loading.value = true;
        const data = await TerminalService.getTerminals();
        terminals.value = data;
    } catch (error) {
        console.error('Ошибка загрузки терминалов:', error);
        terminals.value = [];
    } finally {
        loading.value = false;
    }
};

const createTerminal = () => {
    router.push('/tms/terminals/create');
};

const viewTerminal = (terminal) => {
    router.push(`/tms/terminals/${terminal.id}`);
};

const editTerminal = (terminal) => {
    router.push(`/tms/terminals/${terminal.id}/edit`);
};

const deleteTerminal = async (terminal) => {
    if (confirm(`Вы уверены, что хотите удалить терминал "${terminal.serialNumber}"?`)) {
        try {
            await TerminalService.deleteTerminal(terminal.id);
            console.log('Терминал удален:', terminal.id);
            await loadTerminals();
        } catch (error) {
            console.error('Ошибка удаления терминала:', error);
        }
    }
};

const restartTerminal = async (terminal) => {
    if (confirm(`Вы уверены, что хотите перезагрузить терминал "${terminal.serialNumber}"?`)) {
        try {
            const result = await TerminalService.restartTerminal(terminal.id);
            console.log('Терминал перезагружается:', result.message);
        } catch (error) {
            console.error('Ошибка перезагрузки терминала:', error);
        }
    }
};

const formatDate = (dateString) => {
    if (!dateString) return 'Никогда';
    return new Date(dateString).toLocaleString('ru-RU');
};

const getStatusSeverity = (status) => {
    switch (status) {
        case 'online': return 'success';
        case 'offline': return 'danger';
        case 'maintenance': return 'warning';
        case 'warning': return 'warning';
        default: return 'secondary';
    }
};

const getStatusLabel = (status) => {
    switch (status) {
        case 'online': return 'Онлайн';
        case 'offline': return 'Офлайн';
        case 'maintenance': return 'Обслуживание';
        case 'warning': return 'Предупреждение';
        default: return 'Неизвестно';
    }
};

const getSignalIcon = (signal) => {
    switch (signal) {
        case 'excellent': return 'pi pi-wifi text-green-600';
        case 'good': return 'pi pi-wifi text-blue-600';
        case 'poor': return 'pi pi-wifi text-orange-600';
        case 'none': return 'pi pi-wifi text-red-600';
        default: return 'pi pi-wifi text-gray-400';
    }
};

const getBatteryIcon = (level) => {
    if (level > 75) return 'pi pi-battery-full text-green-600';
    if (level > 50) return 'pi pi-battery-half text-blue-600';
    if (level > 25) return 'pi pi-battery-low text-orange-600';
    if (level > 0) return 'pi pi-battery-empty text-red-600';
    return 'pi pi-battery-empty text-gray-400';
};

const clearFilter = () => {
    filters.value = {
        global: { value: null, matchMode: FilterMatchMode.CONTAINS },
        serialNumber: { value: null, matchMode: FilterMatchMode.CONTAINS },
        location: { value: null, matchMode: FilterMatchMode.CONTAINS },
        status: { value: null, matchMode: FilterMatchMode.EQUALS },
        organizationName: { value: null, matchMode: FilterMatchMode.CONTAINS }
    };
};
</script>

<template>
    <div class="terminal-list">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">Терминалы</h1>
            <Button 
                label="Добавить терминал" 
                icon="pi pi-plus" 
                @click="createTerminal"
            />
        </div>

        <div class="card table-card">
            <div class="flex justify-content-between align-items-center mb-4">
                <div class="flex gap-2">
                    <Button 
                        type="button" 
                        icon="pi pi-filter-slash" 
                        label="Очистить" 
                        outlined 
                        @click="clearFilter"
                    />
                </div>
                <IconField>
                    <InputIcon>
                        <i class="pi pi-search" />
                    </InputIcon>
                    <InputText 
                        v-model="filters.global.value" 
                        placeholder="Поиск по всем полям" 
                    />
                </IconField>
            </div>
            
            <DataTable
                :value="terminals"
                :paginator="true"
                :rows="15"
                dataKey="id"
                :rowHover="true"
                v-model:filters="filters"
                filterDisplay="menu"
                :loading="loading"
                :globalFilterFields="['serialNumber', 'location', 'organizationName', 'driverName', 'vehiclePlate']"
                showGridlines
                responsiveLayout="scroll"
            >
                <template #empty>
                    <div class="text-center p-4">
                        <i class="pi pi-tablet text-4xl text-color-secondary mb-3"></i>
                        <p class="text-color-secondary">Терминалы не найдены</p>
                        <Button 
                            label="Добавить первый терминал" 
                            icon="pi pi-plus" 
                            class="mt-3"
                            @click="createTerminal"
                        />
                    </div>
                </template>
                
                <template #loading>
                    <div class="text-center p-4">
                        <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                        <p>Загрузка данных...</p>
                    </div>
                </template>
                
                <Column field="serialNumber" header="Серийный номер" :sortable="true" style="min-width: 150px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по номеру" 
                        />
                    </template>
                    <template #body="{ data }">
                        <span class="font-mono font-semibold">{{ data.serialNumber }}</span>
                    </template>
                </Column>
                
                <Column field="location" header="Местоположение" :sortable="true" style="min-width: 250px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по местоположению" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div>
                            <div class="font-semibold">{{ data.location }}</div>
                            <small class="text-color-secondary" v-if="data.vehiclePlate">
                                {{ data.vehiclePlate }} • {{ data.driverName }}
                            </small>
                        </div>
                    </template>
                </Column>
                
                <Column field="status" header="Статус" :sortable="true" style="min-width: 120px">
                    <template #filter="{ filterModel }">
                        <Dropdown 
                            v-model="filterModel.value" 
                            :options="statuses" 
                            optionLabel="label" 
                            optionValue="value"
                            placeholder="Выберите статус" 
                            showClear
                        />
                    </template>
                    <template #body="{ data }">
                        <Tag 
                            :value="getStatusLabel(data.status)" 
                            :severity="getStatusSeverity(data.status)" 
                        />
                    </template>
                </Column>
                
                <Column header="Состояние" style="min-width: 150px">
                    <template #body="{ data }">
                        <div class="flex align-items-center gap-2">
                            <div class="flex align-items-center">
                                <i :class="getBatteryIcon(data.batteryLevel)" class="mr-1"></i>
                                <span class="text-sm">{{ data.batteryLevel }}%</span>
                            </div>
                            <div class="flex align-items-center">
                                <i :class="getSignalIcon(data.networkSignal)"></i>
                            </div>
                        </div>
                    </template>
                </Column>
                
                <Column field="organizationName" header="Организация" :sortable="true" style="min-width: 200px">
                    <template #filter="{ filterModel }">
                        <InputText 
                            v-model="filterModel.value" 
                            type="text" 
                            placeholder="Поиск по организации" 
                        />
                    </template>
                    <template #body="{ data }">
                        <div class="flex align-items-center">
                            <i class="pi pi-building mr-2 text-color-secondary"></i>
                            <span>{{ data.organizationName }}</span>
                        </div>
                    </template>
                </Column>
                
                <Column field="lastSeen" header="Последняя активность" :sortable="true" style="min-width: 180px">
                    <template #body="{ data }">
                        <span class="text-sm">{{ formatDate(data.lastSeen) }}</span>
                    </template>
                </Column>
                
                <Column header="Действия" style="min-width: 200px">
                    <template #body="{ data }">
                        <div class="flex gap-1">
                            <Button 
                                icon="pi pi-eye" 
                                size="small" 
                                text 
                                @click="viewTerminal(data)"
                                v-tooltip.top="'Просмотр'"
                            />
                            <Button 
                                icon="pi pi-pencil" 
                                size="small" 
                                text 
                                @click="editTerminal(data)"
                                v-tooltip.top="'Редактировать'"
                            />
                            <Button 
                                icon="pi pi-refresh" 
                                size="small" 
                                text 
                                @click="restartTerminal(data)"
                                v-tooltip.top="'Перезагрузить'"
                                :disabled="data.status === 'offline'"
                            />
                            <Button 
                                icon="pi pi-trash" 
                                size="small" 
                                text 
                                severity="danger" 
                                @click="deleteTerminal(data)"
                                v-tooltip.top="'Удалить'"
                            />
                        </div>
                    </template>
                </Column>
            </DataTable>
        </div>
    </div>
</template>

<style scoped>
.terminal-list {
    height: 100%;
    padding: 1rem;
    display: flex;
    flex-direction: column;
}

.table-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    padding: 1rem !important;
    overflow: hidden;
}

:deep(.p-datatable) {
    height: 100%;
    display: flex;
    flex-direction: column;
}

:deep(.p-datatable .p-datatable-wrapper) {
    flex: 1;
    overflow: auto;
}

:deep(.p-datatable .p-datatable-thead) {
    flex-shrink: 0;
}

:deep(.p-datatable .p-paginator) {
    flex-shrink: 0;
}

.font-mono {
    font-family: 'Courier New', monospace;
}
</style>
