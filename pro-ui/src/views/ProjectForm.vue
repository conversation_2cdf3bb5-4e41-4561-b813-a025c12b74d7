<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ProjectService } from '@/service/ProjectService';
import { ContractService } from '@/service/ContractService';
import { OrganizationService } from '@/service/OrganizationService';

const route = useRoute();
const router = useRouter();

const projectId = route.params.projectId;
const isEdit = computed(() => !!projectId);

const loading = ref(false);
const saving = ref(false);
const contracts = ref([]);
const organizations = ref([]);
const selectedContract = ref(null);

const form = ref({
    name: '',
    code: '',
    contractId: null,
    startDate: '',
    endDate: '',
    manager: '',
    routes: []
});

const errors = ref({});

// Убираем projectTypes - всегда "СберТройка ПРО"

onMounted(async () => {
    await loadContracts();
    await loadOrganizations();
    if (isEdit.value) {
        await loadProject();
    } else {
        // Установим дефолтные даты
        const now = new Date();
        const nextYear = new Date(now.getFullYear() + 1, 11, 31);
        form.value.startDate = now.toISOString().split('T')[0];
        form.value.endDate = nextYear.toISOString().split('T')[0];
    }
});

const loadContracts = async () => {
    try {
        const data = await ContractService.getContracts();
        // Фильтруем только активные договоры, синхронизированные с 1С
        contracts.value = data.filter(contract =>
            contract.status === 'active' &&
            contract.syncStatus === 'synced'
        );
    } catch (error) {
        console.error('Ошибка загрузки договоров:', error);
    }
};

const loadOrganizations = async () => {
    try {
        const data = await OrganizationService.getOrganizations();
        organizations.value = data;
    } catch (error) {
        console.error('Ошибка загрузки организаций:', error);
    }
};

const loadProject = async () => {
    try {
        loading.value = true;
        const project = await ProjectService.getProjectById(projectId);
        if (project) {
            form.value = {
                ...project,
                startDate: project.startDate ? project.startDate.split('T')[0] : '',
                endDate: project.endDate ? project.endDate.split('T')[0] : '',
                routes: project.routes || []
            };
            selectedContract.value = contracts.value.find(c => c.id === project.contractId);
        }
    } catch (error) {
        console.error('Ошибка загрузки проекта:', error);
    } finally {
        loading.value = false;
    }
};

const onContractChange = () => {
    if (selectedContract.value) {
        // Автозаполнение данных из договора
        form.value.contractId = selectedContract.value.id;

        // Генерируем код проекта на основе договора
        const orgCode = selectedContract.value.organizationName
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 10);
        form.value.code = `ST-PRO-${orgCode}-${String(selectedContract.value.id).padStart(3, '0')}`;

        // Предлагаем название проекта
        if (!form.value.name) {
            form.value.name = `СберТройка ПРО ${selectedContract.value.organizationName}`;
        }
    }
};

const validateForm = () => {
    errors.value = {};

    if (!form.value.name.trim()) {
        errors.value.name = 'Наименование проекта обязательно';
    }

    if (!form.value.code.trim()) {
        errors.value.code = 'Код проекта обязателен';
    }

    if (!form.value.contractId) {
        errors.value.contractId = 'Договор обязателен';
    }

    // Убираем проверку organizationId - организации берутся из договора

    if (!form.value.startDate) {
        errors.value.startDate = 'Дата начала обязательна';
    }

    if (!form.value.endDate) {
        errors.value.endDate = 'Дата окончания обязательна';
    }

    if (form.value.startDate && form.value.endDate && form.value.startDate >= form.value.endDate) {
        errors.value.endDate = 'Дата окончания должна быть позже даты начала';
    }

    // Убираем проверку totalBudget - бюджет не задается

    return Object.keys(errors.value).length === 0;
};

const saveProject = async () => {
    if (!validateForm()) {
        return;
    }

    try {
        saving.value = true;

        const projectData = {
            ...form.value,
            projectType: 'transport_system', // Всегда СберТройка ПРО
            startDate: form.value.startDate + 'T00:00:00Z',
            endDate: form.value.endDate + 'T23:59:59Z'
        };

        if (isEdit.value) {
            await ProjectService.updateProject(projectId, projectData);
        } else {
            await ProjectService.createProjectFromContract(form.value.contractId, projectData);
        }

        router.push('/pro');

    } catch (error) {
        console.error('Ошибка сохранения проекта:', error);
    } finally {
        saving.value = false;
    }
};

const cancel = () => {
    router.push('/pro');
};

const generateCode = () => {
    if (selectedContract.value && form.value.contractId) {
        const orgCode = selectedContract.value.organizationName
            .toUpperCase()
            .replace(/[^А-ЯA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .substring(0, 10);
        form.value.code = `ST-PRO-${orgCode}-${String(form.value.contractId).padStart(3, '0')}`;
    }
};

const addRoute = () => {
    const routeName = prompt('Введите название маршрута:');
    if (routeName && !form.value.routes.includes(routeName)) {
        form.value.routes.push(routeName);
    }
};

const removeRoute = (index) => {
    form.value.routes.splice(index, 1);
};

const formatAmount = (amount) => {
    return new Intl.NumberFormat('ru-RU', {
        style: 'currency',
        currency: 'RUB',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
    }).format(amount);
};

const formatDate = (dateString) => {
    if (!dateString) return 'Не указано';
    return new Date(dateString).toLocaleDateString('ru-RU');
};
</script>

<template>
    <div class="project-form">
        <div class="flex justify-content-between align-items-center mb-4">
            <h1 class="text-2xl font-bold m-0">
                {{ isEdit ? 'Редактирование проекта' : 'Создание проекта СберТройка ПРО' }}
            </h1>
            <Button
                label="Назад к списку"
                icon="pi pi-arrow-left"
                outlined
                @click="cancel"
            />
        </div>

        <div class="card" v-if="!loading">
            <form @submit.prevent="saveProject">
                <div class="grid">
                    <div class="col-12">
                        <h3 class="text-lg font-medium mb-3">Основная информация</h3>
                    </div>

                    <div class="col-12">
                        <div class="field">
                            <label for="contractId" class="font-medium">Договор (из 1С) *</label>
                            <Dropdown
                                id="contractId"
                                v-model="selectedContract"
                                :options="contracts"
                                optionLabel="displayName"
                                placeholder="Выберите договор из 1С"
                                :class="{ 'p-invalid': errors.contractId }"
                                class="w-full"
                                @change="onContractChange"
                            >
                                <template #option="{ option }">
                                    <div>
                                        <div class="font-semibold">{{ option.number }}</div>
                                        <div class="text-sm text-color-secondary">{{ option.organizationName }}</div>
                                        <div class="text-sm">{{ formatAmount(option.amount) }} • {{ formatDate(option.validTo) }}</div>
                                    </div>
                                </template>
                            </Dropdown>
                            <small v-if="errors.contractId" class="p-error">{{ errors.contractId }}</small>
                            <small class="text-color-secondary">Только активные договоры, синхронизированные с 1С</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-8">
                        <div class="field">
                            <label for="name" class="font-medium">Наименование проекта *</label>
                            <InputText
                                id="name"
                                v-model="form.name"
                                :class="{ 'p-invalid': errors.name }"
                                placeholder="СберТройка ПРО г. Москва"
                                class="w-full"
                            />
                            <small v-if="errors.name" class="p-error">{{ errors.name }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-4">
                        <div class="field">
                            <label for="code" class="font-medium">Код проекта *</label>
                            <InputText
                                id="code"
                                v-model="form.code"
                                :class="{ 'p-invalid': errors.code }"
                                placeholder="ST-PRO-MSK-001"
                                class="w-full"
                                @blur="generateCode"
                            />
                            <small v-if="errors.code" class="p-error">{{ errors.code }}</small>
                        </div>
                    </div>

                    <!-- Убираем описание проекта -->

                    <!-- Убираем тип проекта - всегда "СберТройка ПРО" -->

                    <!-- Убираем организацию - у проекта их много, показываем только оператора -->

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="startDate" class="font-medium">Дата начала *</label>
                            <Calendar
                                id="startDate"
                                v-model="form.startDate"
                                :class="{ 'p-invalid': errors.startDate }"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                            <small v-if="errors.startDate" class="p-error">{{ errors.startDate }}</small>
                        </div>
                    </div>

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="endDate" class="font-medium">Дата окончания *</label>
                            <Calendar
                                id="endDate"
                                v-model="form.endDate"
                                :class="{ 'p-invalid': errors.endDate }"
                                dateFormat="dd.mm.yy"
                                class="w-full"
                            />
                            <small v-if="errors.endDate" class="p-error">{{ errors.endDate }}</small>
                        </div>
                    </div>

                    <!-- Убираем общий бюджет -->

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label for="manager" class="font-medium">Руководитель проекта</label>
                            <InputText
                                id="manager"
                                v-model="form.manager"
                                placeholder="Иванов И.И."
                                class="w-full"
                            />
                        </div>
                    </div>

                    <!-- Убираем количество ТС - должно считаться автоматически -->

                    <div class="col-12 md:col-6">
                        <div class="field">
                            <label class="font-medium">Маршруты</label>
                            <div class="flex gap-2 mb-2">
                                <Button
                                    label="Добавить маршрут"
                                    icon="pi pi-plus"
                                    size="small"
                                    @click="addRoute"
                                />
                            </div>
                            <div class="flex flex-wrap gap-2" v-if="form.routes.length > 0">
                                <Tag
                                    v-for="(route, index) in form.routes"
                                    :key="index"
                                    :value="route"
                                    removable
                                    @remove="removeRoute(index)"
                                />
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex justify-content-end gap-2 mt-4">
                    <Button
                        label="Отмена"
                        icon="pi pi-times"
                        outlined
                        @click="cancel"
                        :disabled="saving"
                    />
                    <Button
                        type="submit"
                        :label="isEdit ? 'Сохранить' : 'Создать проект'"
                        :icon="isEdit ? 'pi pi-save' : 'pi pi-plus'"
                        :loading="saving"
                    />
                </div>
            </form>
        </div>

        <div v-else class="card">
            <div class="text-center p-4">
                <i class="pi pi-spin pi-spinner text-4xl text-primary mb-3"></i>
                <p>Загрузка данных проекта...</p>
            </div>
        </div>
    </div>
</template>

<style scoped>
.project-form {
    height: 100%;
    padding: 1rem;
    overflow: auto;
}

.field {
    margin-bottom: 1rem;
}

.field label {
    display: block;
    margin-bottom: 0.5rem;
}

.p-error {
    color: #e24c4c;
    font-size: 0.875rem;
}
</style>
