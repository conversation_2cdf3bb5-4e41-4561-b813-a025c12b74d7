version: '3.8'

services:
  pro-ui:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        VITE_KEYCLOAK_URL: ${KEYCLOAK_URL:-https://dev-auth.sbertroika.tech/}
        VITE_KEYCLOAK_REALM: ${K<PERSON><PERSON><PERSON>OAK_REALM:-test-asop}
        VITE_KEYCLOAK_CLIENT_ID: ${KEY<PERSON>OAK_CLIENT_ID:-crm-ui-local}
    ports:
      - "80:80"
    environment:
      # Переменные для переопределения конфигурации Keycloak в runtime
      KEYCLOAK_URL: ${KEYCLOAK_URL:-https://dev-auth.sbertroika.tech/}
      KEYCLOAK_REALM: ${KEYCLOAK_REALM:-test-asop}
      KEYCLOAK_CLIENT_ID: ${KEYCLOAK_CLIENT_ID:-crm-ui-local}
    restart: unless-stopped 