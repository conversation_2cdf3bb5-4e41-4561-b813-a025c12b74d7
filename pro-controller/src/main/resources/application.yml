server:
  port: 8080

spring:
  application:
    name: pro-controller

  jackson:
    default-property-inclusion: NON_NULL
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false
  
  # R2DBC конфигурация
  r2dbc:
    url: r2dbc:${DB_URL:postgresql://localhost:5432/pro}
    username: ${DB_USER:postgres}
    password: ${DB_PASSWORD:postgres}

  flyway:
    enabled: ${DB_MIGRATION_ENABLE:false}


# Keycloak конфигурация
keycloak:
  auth-server-url: ${KEYCLOAK_URL:https://dev-auth.sbertroika.tech}
  realm: ${KEYCLOAK_REALM:test-asop}
  client-id: ${KEYCLOAK_CLIENT_ID:crm-ui-local}

logging:
  structured:
    format:
      console: ecs
      file: ecs
  level:
    root: ${LOG_LEVEL:INFO}
    io.r2dbc.postgresql: ${LOG_LEVEL:INFO}
    org.zalando.logbook: TRACE

logbook:
  predicate:
    include:
      - path: /**
        methods:
          - GET
          - POST
  filter.enabled: false
  secure-filter.enabled: true
  format.style: json
  strategy: default
  minimum-status: 200
  obfuscate:
    headers:
      - Authorization

management:
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      group:
        liveness:
          include: '*'
        readiness:
          include: '*'