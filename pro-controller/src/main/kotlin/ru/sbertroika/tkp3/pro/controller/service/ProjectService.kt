package ru.sbertroika.tkp3.pro.controller.service

import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.ProjectRepository
import ru.sbertroika.tkp3.pro.controller.output.ProjectOperatorRepository
import ru.sbertroika.tkp3.pro.model.Project
import ru.sbertroika.tkp3.pro.model.ProjectStatus
import java.sql.Timestamp
import java.time.LocalDateTime
import java.util.*

@Service
class ProjectService(
    private val projectRepository: ProjectRepository,
    private val projectOperatorRepository: ProjectOperatorRepository
) {

    /**
     * Получить все проекты с фильтрацией и пагинацией
     */
    fun getAllProjects(
        status: String?,
        region: String?,
        projectType: String?,
        page: Int,
        size: Int
    ): Flux<Project> {
        val offset = page * size
        return projectRepository.findAllProjectsWithFilters(
            status,
            region,
            projectType,
            size,
            offset
        ).flatMap { project ->
            // Заполняем операторскую организацию
            projectOperatorRepository.findLatestByProjectId(project.id!!)
                .next()
                .map { operator ->
                    project.operatorOrganization = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                        id = operator.operatorId,
                        name = operator.operatorName,
                        role = operator.operatorRole
                    )
                    project
                }
                .defaultIfEmpty(project)
        }
    }

    /**
     * Получить общее количество проектов с фильтрацией
     */
    fun getProjectsCount(
        status: String?,
        region: String?,
        projectType: String?
    ): Mono<Long> {
        return projectRepository.countProjectsWithFilters(
            status,
            region,
            projectType
        ).next()
    }

    /**
     * Получить проект по ID
     */
    fun getProjectById(id: UUID): Mono<Project> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                // Заполняем операторскую организацию
                projectOperatorRepository.findLatestByProjectId(project.id!!)
                    .next()
                    .map { operator ->
                        project.operatorOrganization = ru.sbertroika.tkp3.pro.model.OperatorOrganization(
                            id = operator.operatorId,
                            name = operator.operatorName,
                            role = operator.operatorRole
                        )
                        project
                    }
                    .defaultIfEmpty(project)
            }
    }

    /**
     * Создать новый проект
     */
    fun createProject(project: Project): Mono<Project> {
        val now = Timestamp.valueOf(LocalDateTime.now())
        project.id = UUID.randomUUID()
        project.version = 1
        project.versionCreatedAt = now
        project.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
        project.syncStatus = "pending"
        
        return projectRepository.save(project)
    }

    /**
     * Обновить проект
     */
    fun updateProject(id: UUID, updatedProject: Project): Mono<Project> {
        return projectRepository.findProjectById(id)
            .flatMap { existingProject ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                updatedProject.id = id
                updatedProject.version = (existingProject.version ?: 0) + 1
                updatedProject.versionCreatedAt = now
                updatedProject.versionCreatedBy = UUID.randomUUID() // TODO: получить из контекста безопасности
                updatedProject.lastSyncDate = now
                
                projectRepository.save(updatedProject)
            }
    }

    /**
     * Удалить проект (логическое удаление)
     */
    fun deleteProject(id: UUID): Mono<Void> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.IS_DELETED
                projectRepository.save(project)
            }
            .then()
    }

    /**
     * Синхронизировать проект с договором
     */
    fun syncProjectWithContract(id: UUID): Mono<Project> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                val now = Timestamp.valueOf(LocalDateTime.now())
                project.lastSyncDate = now
                project.syncStatus = "synced"
                projectRepository.save(project)
            }
    }

    /**
     * Активировать проект
     */
    fun activateProject(id: UUID): Mono<Project> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.ACTIVE
                projectRepository.save(project)
            }
    }

    /**
     * Завершить проект
     */
    fun completeProject(id: UUID): Mono<Project> {
        return projectRepository.findProjectById(id)
            .flatMap { project ->
                project.status = ProjectStatus.COMPLETED
                projectRepository.save(project)
            }
    }

    /**
     * Получить проекты по статусу
     */
    fun getProjectsByStatus(status: String): Flux<Project> {
        return projectRepository.findByStatus(status)
    }

    /**
     * Получить проекты по региону
     */
    fun getProjectsByRegion(region: String): Flux<Project> {
        return projectRepository.findByRegion(region)
    }

    /**
     * Получить проекты по типу
     */
    fun getProjectsByType(projectType: String): Flux<Project> {
        return projectRepository.findByProjectType(projectType)
    }

    /**
     * Получить проекты по договору
     */
    fun getProjectsByContract(contractId: UUID): Flux<Project> {
        return projectRepository.findByContractId(contractId)
    }

    /**
     * Получить организации проекта
     */
    fun getProjectOrganizations(id: UUID): Mono<List<Map<String, Any>>> {
        return projectRepository.findProjectById(id)
            .map { 
                // TODO: Реализовать получение организаций из базы данных
                // Пока возвращаем пустой список
                emptyList<Map<String, Any>>()
            }
    }
} 