package ru.sbertroika.tkp3.pro.controller.input

import org.springframework.web.bind.annotation.*
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.controller.output.model.ApiResponse
import ru.sbertroika.tkp3.pro.controller.service.ProjectService
import ru.sbertroika.tkp3.pro.model.Project
import java.util.*

@RestController
@RequestMapping("/api/v1/pro")
class ProjectController(
    private val projectService: ProjectService
) : BaseController() {

    @GetMapping("/projects")
    fun getProjects(
        @RequestParam(required = false) status: String?,
        @RequestParam(required = false) region: String?,
        @RequestParam(required = false) projectType: String?,
        @RequestParam(defaultValue = "0") page: Int,
        @RequestParam(defaultValue = "20") size: Int
    ): Mono<ApiResponse<Map<String, Any>>> {
        logUserAction("запрашивает список проектов")
        
        return Mono.zip(
            projectService.getAllProjects(status, region, projectType, page, size).collectList(),
            projectService.getProjectsCount(status, region, projectType)
        ).map { result ->
            val projects = result.t1
            val totalCount = result.t2
            val totalPages = (totalCount + size - 1) / size
            val response = mapOf(
                "content" to projects,
                "pagination" to mapOf(
                    "page" to page,
                    "size" to size,
                    "totalElements" to totalCount,
                    "totalPages" to totalPages,
                    "hasNext" to (page < totalPages - 1),
                    "hasPrevious" to (page > 0)
                )
            )
            ApiResponse.success(response)
        }.onErrorResume { error ->
            logError("Ошибка при получении проектов: ${error.message}", error)
            Mono.just(ApiResponse.error<Map<String, Any>>("Ошибка получения проектов: ${error.message}"))
        }
    }

    @GetMapping("/projects/{id}")
    fun getProjectById(@PathVariable id: String): Mono<ApiResponse<Project>> {
        return projectService.getProjectById(UUID.fromString(id))
            .map { project ->
                ApiResponse.success(project)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Project>("Ошибка получения проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects")
    fun createProject(@RequestBody project: Project): Mono<ApiResponse<Project>> {
        logUserAction("создает новый проект", project.name)
        
        return projectService.createProject(project)
            .map { createdProject ->
                logUserAction("успешно создал проект", createdProject.name)
                ApiResponse.success(createdProject)
            }
            .onErrorResume { error ->
                logError("Ошибка при создании проекта: ${error.message}", error)
                Mono.just(ApiResponse.error<Project>("Ошибка создания проекта: ${error.message}"))
            }
    }

    @PutMapping("/projects/{id}")
    fun updateProject(@PathVariable id: String, @RequestBody project: Project): Mono<ApiResponse<Project>> {
        return projectService.updateProject(UUID.fromString(id), project)
            .map { updatedProject ->
                ApiResponse.success(updatedProject)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Project>("Ошибка обновления проекта: ${error.message}"))
            }
    }

    @DeleteMapping("/projects/{id}")
    fun deleteProject(@PathVariable id: String): Mono<ApiResponse<String>> {
        return projectService.deleteProject(UUID.fromString(id))
            .map {
                ApiResponse.success("Проект успешно удален")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<String>("Ошибка удаления проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/sync")
    fun syncProjectWithContract(@PathVariable id: String): Mono<ApiResponse<Project>> {
        return projectService.syncProjectWithContract(UUID.fromString(id))
            .map { project ->
                ApiResponse.success(project, "Проект синхронизирован с договором")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Project>("Ошибка синхронизации проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/activate")
    fun activateProject(@PathVariable id: String): Mono<ApiResponse<Project>> {
        return projectService.activateProject(UUID.fromString(id))
            .map { project ->
                ApiResponse.success(project, "Проект активирован")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Project>("Ошибка активации проекта: ${error.message}"))
            }
    }

    @PostMapping("/projects/{id}/complete")
    fun completeProject(@PathVariable id: String): Mono<ApiResponse<Project>> {
        return projectService.completeProject(UUID.fromString(id))
            .map { project ->
                ApiResponse.success(project, "Проект завершен")
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<Project>("Ошибка завершения проекта: ${error.message}"))
            }
    }

    @GetMapping("/projects/status/{status}")
    fun getProjectsByStatus(@PathVariable status: String): Mono<ApiResponse<List<Project>>> {
        return projectService.getProjectsByStatus(status)
            .collectList()
            .map { projects ->
                ApiResponse.success(projects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Project>>("Ошибка получения проектов по статусу: ${error.message}"))
            }
    }

    @GetMapping("/projects/region/{region}")
    fun getProjectsByRegion(@PathVariable region: String): Mono<ApiResponse<List<Project>>> {
        return projectService.getProjectsByRegion(region)
            .collectList()
            .map { projects ->
                ApiResponse.success(projects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Project>>("Ошибка получения проектов по региону: ${error.message}"))
            }
    }

    @GetMapping("/projects/type/{projectType}")
    fun getProjectsByType(@PathVariable projectType: String): Mono<ApiResponse<List<Project>>> {
        return projectService.getProjectsByType(projectType)
            .collectList()
            .map { projects ->
                ApiResponse.success(projects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Project>>("Ошибка получения проектов по типу: ${error.message}"))
            }
    }

    @GetMapping("/projects/contract/{contractId}")
    fun getProjectsByContract(@PathVariable contractId: String): Mono<ApiResponse<List<Project>>> {
        return projectService.getProjectsByContract(UUID.fromString(contractId))
            .collectList()
            .map { projects ->
                ApiResponse.success(projects)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Project>>("Ошибка получения проектов по договору: ${error.message}"))
            }
    }

    @GetMapping("/projects/{id}/organizations")
    fun getProjectOrganizations(@PathVariable id: String): Mono<ApiResponse<List<Map<String, Any>>>> {
        return projectService.getProjectOrganizations(UUID.fromString(id))
            .map { organizations ->
                ApiResponse.success(organizations)
            }
            .onErrorResume { error ->
                Mono.just(ApiResponse.error<List<Map<String, Any>>>("Ошибка получения организаций проекта: ${error.message}"))
            }
    }
} 