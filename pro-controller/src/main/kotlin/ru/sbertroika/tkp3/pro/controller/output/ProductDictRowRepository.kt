package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.core.R2dbcEntityTemplate
import org.springframework.data.relational.core.query.Criteria
import org.springframework.data.relational.core.query.Query
import org.springframework.stereotype.Repository
import org.springframework.stereotype.Service
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import ru.sbertroika.tkp3.pro.model.ProductDictRow
import ru.sbertroika.tkp3.pro.model.ProductDictRowPK
import ru.sbertroika.tkp3.pro.model.PayMethodType
import java.time.LocalDateTime
import java.util.*

@Service
open class ProductDictRowRepository(
    private val template: R2dbcEntityTemplate
) {

    /**
     * Найти все последние версии ProductDictRow
     */
    fun findAllLatestVersions(limit: Int, offset: Long): Flux<ProductDictRow> {
        val sql = """
            SELECT pdr.*, p.p_name as product_name, t.t_name as tariff_name
            FROM product_dict_row pdr
            LEFT JOIN product p ON pdr.p_id = p.p_id AND p.p_version = (
                SELECT MAX(p2.p_version) FROM product p2 WHERE p2.p_id = p.p_id
            )
            LEFT JOIN tariff t ON pdr.t_id = t.t_id AND t.t_version = (
                SELECT MAX(t2.t_version) FROM tariff t2 WHERE t2.t_id = t.t_id
            )
            WHERE pdr.pdr_version = (
                SELECT MAX(pdr2.pdr_version) 
                FROM product_dict_row pdr2 
                WHERE pdr2.pdr_id = pdr.pdr_id
            )
            ORDER BY pdr.pdr_id
            LIMIT :limit OFFSET :offset
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("limit", limit)
            .bind("offset", offset)
            .map { row, _ ->
                ProductDictRow(
                    id = row.get("pdr_id", UUID::class.java),
                    version = row.get("pdr_version", Int::class.java),
                    versionCreatedAt = row.get("pdr_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdr_version_created_by", UUID::class.java),
                    projectId = row.get("pdr_project_id", UUID::class.java),
                    productId = row.get("p_id", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    paymentMethodType = row.get("pdr_method_type", String::class.java)?.let { PayMethodType.valueOf(it) },
                    isFixPrice = row.get("pdr_fix_price", Boolean::class.java),
                    price = row.get("pdr_price", Long::class.java),
                    tags = row.get("tags", String::class.java),
                    productName = row.get("product_name", String::class.java),
                    tariffName = row.get("tariff_name", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти ProductDictRow по ID последней версии
     */
    fun findByIdLatestVersion(id: UUID): Mono<ProductDictRow> {
        val sql = """
            SELECT pdr.*, p.p_name as product_name, t.t_name as tariff_name
            FROM product_dict_row pdr
            LEFT JOIN product p ON pdr.p_id = p.p_id AND p.p_version = (
                SELECT MAX(p2.p_version) FROM product p2 WHERE p2.p_id = p.p_id
            )
            LEFT JOIN tariff t ON pdr.t_id = t.t_id AND t.t_version = (
                SELECT MAX(t2.t_version) FROM tariff t2 WHERE t2.t_id = t.t_id
            )
            WHERE pdr.pdr_id = :id
            AND pdr.pdr_version = (
                SELECT MAX(pdr2.pdr_version) 
                FROM product_dict_row pdr2 
                WHERE pdr2.pdr_id = :id
            )
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ ->
                ProductDictRow(
                    id = row.get("pdr_id", UUID::class.java),
                    version = row.get("pdr_version", Int::class.java),
                    versionCreatedAt = row.get("pdr_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdr_version_created_by", UUID::class.java),
                    projectId = row.get("pdr_project_id", UUID::class.java),
                    productId = row.get("p_id", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    paymentMethodType = row.get("pdr_method_type", String::class.java)?.let { PayMethodType.valueOf(it) },
                    isFixPrice = row.get("pdr_fix_price", Boolean::class.java),
                    price = row.get("pdr_price", Long::class.java),
                    tags = row.get("tags", String::class.java),
                    productName = row.get("product_name", String::class.java),
                    tariffName = row.get("tariff_name", String::class.java)
                ).also { println("Created ProductDictRow: $it") }
            }
            .one()
            .doOnError { error ->
                println("Error in findByIdLatestVersion: ${error.message}")
                println("Stack trace: ${error.stackTraceToString()}")
                println("=== ProductDictRowRepository.findByIdLatestVersion ERROR ===")
            }
    }

    /**
     * Найти ProductDictRow по тарифу
     */
    fun findByTariffId(tariffId: UUID): Flux<ProductDictRow> {
        val sql = """
            SELECT pdr.*, p.p_name as product_name, t.t_name as tariff_name
            FROM product_dict_row pdr
            LEFT JOIN product p ON pdr.p_id = p.p_id AND p.p_version = (
                SELECT MAX(p2.p_version) FROM product p2 WHERE p2.p_id = p.p_id
            )
            LEFT JOIN tariff t ON pdr.t_id = t.t_id AND t.t_version = (
                SELECT MAX(t2.t_version) FROM tariff t2 WHERE t2.t_id = t.t_id
            )
            WHERE pdr.t_id = :tariffId
            AND pdr.pdr_version = (
                SELECT MAX(pdr2.pdr_version) 
                FROM product_dict_row pdr2 
                WHERE pdr2.pdr_id = pdr.pdr_id
            )
            ORDER BY pdr.pdr_id
        """.trimIndent()
        
        return template.databaseClient
            .sql(sql)
            .bind("tariffId", tariffId)
            .map { row, _ ->
                ProductDictRow(
                    id = row.get("pdr_id", UUID::class.java),
                    version = row.get("pdr_version", Int::class.java),
                    versionCreatedAt = row.get("pdr_version_created_at", LocalDateTime::class.java)?.let { java.sql.Timestamp.valueOf(it) },
                    versionCreatedBy = row.get("pdr_version_created_by", UUID::class.java),
                    projectId = row.get("pdr_project_id", UUID::class.java),
                    productId = row.get("p_id", UUID::class.java),
                    tariffId = row.get("t_id", UUID::class.java),
                    paymentMethodType = row.get("pdr_method_type", String::class.java)?.let { PayMethodType.valueOf(it) },
                    isFixPrice = row.get("pdr_fix_price", Boolean::class.java),
                    price = row.get("pdr_price", Long::class.java),
                    tags = row.get("tags", String::class.java),
                    productName = row.get("product_name", String::class.java),
                    tariffName = row.get("tariff_name", String::class.java)
                )
            }
            .all()
    }

    /**
     * Найти максимальную версию по ID
     */
    fun findMaxVersionById(id: UUID): Mono<Int?> {
        println("=== ProductDictRowRepository.findMaxVersionById START ===")
        println("Looking for max version for ID: $id")
        
        val sql = "SELECT MAX(pdr_version) FROM product_dict_row WHERE pdr_id = :id"
        println("SQL query: $sql")
        
        return template.databaseClient
            .sql(sql)
            .bind("id", id)
            .map { row, _ -> 
                val result = row.get(0, Int::class.java)
                println("Max version result: $result")
                result
            }
            .one()
            .doOnNext { result ->
                println("Successfully found max version: $result")
                println("=== ProductDictRowRepository.findMaxVersionById END ===")
            }
            .doOnError { error ->
                println("Error in findMaxVersionById: ${error.message}")
                println("Stack trace: ${error.stackTraceToString()}")
                println("=== ProductDictRowRepository.findMaxVersionById ERROR ===")
            }
    }

    /**
     * Сохранить ProductDictRow
     */
    fun save(productDictRow: ProductDictRow): Mono<ProductDictRow> {
        return template.insert(ProductDictRow::class.java)
            .using(productDictRow)
            .doOnError { error ->
                println("Error in save: ${error.message}")
                println("Stack trace: ${error.stackTraceToString()}")
                println("=== ProductDictRowRepository.save ERROR ===")
            }
    }

    /**
     * Удалить ProductDictRow (логическое удаление)
     */
    fun delete(id: UUID): Mono<Void> {
        return findByIdLatestVersion(id)
            .flatMap { existing ->
                findMaxVersionById(id)
                    .flatMap { maxVersion ->
                        val deleted = existing.copy(
                            version = (maxVersion ?: 0) + 1,
                            versionCreatedAt = java.sql.Timestamp(System.currentTimeMillis()),
                            tags = "DELETED"
                        )
                        save(deleted).then()
                    }
            }
    }
} 