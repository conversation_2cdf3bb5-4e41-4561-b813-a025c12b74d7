package ru.sbertroika.tkp3.pro.controller.output

import org.springframework.data.r2dbc.repository.Query
import org.springframework.data.repository.reactive.ReactiveCrudRepository
import org.springframework.stereotype.Repository
import ru.sbertroika.tkp3.pro.model.Project
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import java.util.*

@Repository
interface ProjectRepository : ReactiveCrudRepository<Project, UUID> {

    @Query("""
        SELECT * FROM project 
        WHERE pr_status != 'IS_DELETED'::project_status
        AND (:status IS NULL OR pr_status = :status::project_status)
        AND (:region IS NULL OR pr_region = :region)
        AND (:projectType IS NULL OR pr_project_type = :projectType)
        ORDER BY pr_version_created_at DESC
        LIMIT :limit OFFSET :offset
    """)
    fun findAllProjectsWithFilters(
        status: String?,
        region: String?,
        projectType: String?,
        limit: Int,
        offset: Int
    ): Flux<Project>

    @Query("""
        SELECT COUNT(*) FROM project 
        WHERE pr_status != 'IS_DELETED'::project_status
        AND (:status IS NULL OR pr_status = :status::project_status)
        AND (:region IS NULL OR pr_region = :region)
        AND (:projectType IS NULL OR pr_project_type = :projectType)
    """)
    fun countProjectsWithFilters(
        status: String?,
        region: String?,
        projectType: String?
    ): Flux<Long>

    @Query("SELECT * FROM project WHERE pr_id = :id AND pr_status != 'IS_DELETED'::project_status")
    fun findProjectById(id: UUID): Mono<Project>

    @Query("SELECT * FROM project WHERE pr_status = :status::project_status ORDER BY pr_version_created_at DESC")
    fun findByStatus(status: String): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_contract_id = :contractId AND pr_status != 'IS_DELETED'::project_status")
    fun findByContractId(contractId: UUID): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_region = :region AND pr_status != 'IS_DELETED'::project_status ORDER BY pr_version_created_at DESC")
    fun findByRegion(region: String): Flux<Project>

    @Query("SELECT * FROM project WHERE pr_project_type = :projectType AND pr_status != 'IS_DELETED'::project_status ORDER BY pr_version_created_at DESC")
    fun findByProjectType(projectType: String): Flux<Project>
} 